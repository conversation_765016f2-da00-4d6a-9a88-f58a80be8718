<?php

namespace App\Http\Livewire\Programs;

use App\Models\Audio\Podcast;
use App\Services\Seo\SeoMetaService;
use App\Services\Users\UserJourneysService;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Date;
use Livewire\Component;

class Index extends Component
{
    public bool $initialized = false;

    public ?int $selectedRadioStationId = null;

    public ?string $selectedDay = null;

    public Collection $podcasts;

    public array $daysOfWeek = [];

    public array $visibleDays = [];

    public int $currentWeekOffset = 0;

    public int $daysToShow = 7;

    public int $totalDaysRange = 21; // 3 semaines de données

    public array $selectedPodcastIds = [];

    public ?string $playedAudioSourceClass = null;

    public ?int $playedAudioSourceId = null;

    public bool $pauseAllPodcasts = false;

    protected $listeners = [
        'player:audio:source:updated' => 'updatePlayedAudioSource',
        'player:audio:source:paused' => 'playerHasPaused',
        'player:audio:source:played' => 'playerIsResuming',
        'radio:station:universe:updated' => 'setSelectedRadioStationId',
    ];

    public function mount(): void
    {
        [
            'played_audio_source_class' => $audioSourceClass,
            'played_audio_source_id' => $audioSourceId,
        ] = app(UserJourneysService::class)->getPlayedAudioSource();
        $this->setPlayedAudioSource($audioSourceClass, $audioSourceId);
    }

    protected function setPlayedAudioSource(?string $audioSourceClass, ?int $audioSourceId): void
    {
        $this->playedAudioSourceClass = $audioSourceClass;
        $this->playedAudioSourceId = $audioSourceId;
    }

    /** @throws \Exception */
    public function init(): void
    {
        app(SeoMetaService::class)->generateSeoMeta(routeKey: 'programs', livewireComponent: $this);
        $selectedRadioStationId = app(UserJourneysService::class)->getSelectedRadioStationUniverseId();
        $this->setSelectedRadioStationId($selectedRadioStationId);
        $this->setDaysOfCurrentWeek();
        $this->setVisibleDays();
        $this->selectedDay = Date::today()->toDateString();
        $this->initialized = true;
    }

    public function setSelectedRadioStationId(int $selectedRadioStationId): void
    {
        $this->selectedRadioStationId = $selectedRadioStationId;
    }

    protected function setDaysOfCurrentWeek(): void
    {
        // Générer une plage plus large de jours (3 semaines par défaut)
        $startDate = Date::now()->sub('days', 10); // 10 jours dans le passé
        $daysOfWeek = [];
        for ($day = 0; $day < $this->totalDaysRange; $day++) {
            $daysOfWeek[] = $startDate->copy()->add('days', $day)->toDateString();
        }
        $this->daysOfWeek = $daysOfWeek;
    }

    protected function setVisibleDays(): void
    {
        $startIndex = $this->currentWeekOffset * $this->daysToShow;
        $this->visibleDays = array_slice($this->daysOfWeek, $startIndex, $this->daysToShow);
    }

    public function updateSelectedDay(string $selectedDay): void
    {
        $this->selectedDay = $selectedDay;
        $this->setPodcasts();
    }

    public function navigateToPreviousPeriod(): void
    {
        if ($this->currentWeekOffset > 0) {
            $this->currentWeekOffset--;
            $this->setVisibleDays();

            // Si le jour sélectionné n'est plus visible, sélectionner le premier jour visible
            if (!in_array($this->selectedDay, $this->visibleDays)) {
                $this->selectedDay = $this->visibleDays[0];
                $this->setPodcasts();
            }
        }
    }

    public function navigateToNextPeriod(): void
    {
        $maxOffset = floor(count($this->daysOfWeek) / $this->daysToShow) - 1;
        if ($this->currentWeekOffset < $maxOffset) {
            $this->currentWeekOffset++;
            $this->setVisibleDays();

            // Si le jour sélectionné n'est plus visible, sélectionner le premier jour visible
            if (!in_array($this->selectedDay, $this->visibleDays)) {
                $this->selectedDay = $this->visibleDays[0];
                $this->setPodcasts();
            }
        }
    }

    public function canNavigateToPrevious(): bool
    {
        return $this->currentWeekOffset > 0;
    }

    public function canNavigateToNext(): bool
    {
        $maxOffset = floor(count($this->daysOfWeek) / $this->daysToShow) - 1;
        return $this->currentWeekOffset < $maxOffset;
    }

    public function setPodcasts(): void
    {
        $this->podcasts = Podcast::with(['media', 'program.subPrograms'])
            ->where('type', Podcast::TYPE_REPLAY)
            ->whereDate('published_at', Date::parse($this->selectedDay))
            ->where('active', true)
            ->where(function (Builder $subWhereQuery) {
                $subWhereQuery->whereRelation(
                    'radioStations',
                    fn (Builder $radioStationQuery) => $radioStationQuery->where('id', $this->selectedRadioStationId)
                )->orWhereDoesntHave('radioStations');
            })
            ->orderBy('published_at')
            ->get();
        $this->updatePodcastStatuses();
    }

    public function updatePodcastStatuses(): void
    {
        if ($this->pauseAllPodcasts) {
            $this->podcasts->map(function (Podcast $podcast) {
                $podcast->programSelected = in_array($podcast->id, $this->selectedPodcastIds, true);
                $podcast->playing = false;
                $podcast->subPodcasts = $this->podcasts->whereIn(
                    'program_id',
                    $podcast->program->subPrograms->pluck('id')
                );

                return $podcast;
            });

            return;
        }
        $this->selectedPodcastIds = [];
        $this->podcasts = $this->podcasts->map(function (Podcast $podcast) {
            $isPlaying = $podcast::class === $this->playedAudioSourceClass
                && $podcast->id === $this->playedAudioSourceId;
            if ($isPlaying) {
                $this->selectedPodcastIds[] = $podcast->id;
            }
            $podcast->programSelected = $isPlaying;
            $podcast->playing = $isPlaying && app(UserJourneysService::class)->getPlayerPlayingStatus();
            $podcast->subPodcasts = $this->podcasts->whereIn('program_id', $podcast->program->subPrograms->pluck('id'));

            return $podcast;
        });
    }

    public function updatePlayedAudioSource(
        string $audioSourceClass,
        int $audioSourceId
    ): void {
        $this->setPlayedAudioSource($audioSourceClass, $audioSourceId);
        $this->pauseAllPodcasts = false;
    }

    public function playerHasPaused(): void
    {
        $this->pauseAllPodcasts = true;
    }

    public function playerIsResuming(): void
    {
        $this->pauseAllPodcasts = false;
    }

    public function render(): View
    {
        if ($this->initialized) {
            $this->setPodcasts();
        }

        return view('livewire.programs.index');
    }
}
