<?php

namespace App\Http\Livewire\Tables;

use App\Models\Logs\LogContactFormMessage;
use Illuminate\Database\Eloquent\Builder;
use Parsedown;
use Rappasoft\LaravelLivewireTables\DataTableComponent;
use Rappasoft\LaravelLivewireTables\Views\Column;

class ContactFormLogsTable extends DataTableComponent
{
    protected $model = LogContactFormMessage::class;

    public function configure(): void
    {
        $this->setPrimaryKey('id')
            ->setDefaultSort('created_at', 'desc');

        $this->setTableAttributes([
            'default' => false,
            'class' => 'table align-middle',
        ]);
        $this->setTheadAttributes([
            'default' => false,
            'class' => 'table-light border-top border-bottom',
        ]);

        $this->emit('livewire:table:refresh');
    }

    public function columns(): array
    {
        return [
            Column::make('ID', 'id')
                ->sortable()
                ->setSortingPillDirections('0-9', '9-0')
                ->collapseOnMobile()
                ->format(fn ($value, $row, Column $column) => '<strong>' . $value . '</strong>')
                ->html(),
            Column::make('Contact', 'data')
                ->format(fn ($value, $row, Column $column) => $this->formatContactData($value))
                ->html()
                ->searchable(function (Builder $builder, string $searchTerm) {
                    $searchTerm = strtolower($searchTerm);

                    return $builder->when(config('database.default') === 'pgsql', function ($query) use ($searchTerm) {
                        return $query
                            ->whereRaw("unaccent(lower(data->>'email')) ILIKE unaccent(lower(?))", ["%{$searchTerm}%"])
                            ->orWhereRaw("unaccent(lower(data->>'phone_number')) ILIKE unaccent(lower(?))", ["%{$searchTerm}%"])
                            ->orWhereRaw("unaccent(lower(data->>'message')) ILIKE unaccent(lower(?))", ["%{$searchTerm}%"])
                            ->orWhereRaw(
                                "unaccent(lower(COALESCE(data->>'first_name', '') || ' ' || COALESCE(data->>'last_name', ''))) ILIKE unaccent(lower(?))",
                                ["%{$searchTerm}%"]
                            )
                            ->orWhereRaw(
                                "unaccent(lower(COALESCE(data->>'last_name', '') || ' ' || COALESCE(data->>'first_name', ''))) ILIKE unaccent(lower(?))",
                                ["%{$searchTerm}%"]
                            );
                    }, function ($query) use ($searchTerm) {
                        return $query
                            ->whereRaw("LOWER(JSON_UNQUOTE(JSON_EXTRACT(data, '$.email'))) LIKE ?", ["%{$searchTerm}%"])
                            ->orWhereRaw("LOWER(JSON_UNQUOTE(JSON_EXTRACT(data, '$.phone_number'))) LIKE ?", ["%{$searchTerm}%"])
                            ->orWhereRaw("LOWER(JSON_UNQUOTE(JSON_EXTRACT(data, '$.message'))) LIKE ?", ["%{$searchTerm}%"])
                            ->orWhereRaw("LOWER(CONCAT(JSON_UNQUOTE(JSON_EXTRACT(data, '$.first_name')), ' ', JSON_UNQUOTE(JSON_EXTRACT(data, '$.last_name')))) LIKE ?", ["%{$searchTerm}%"])
                            ->orWhereRaw("LOWER(CONCAT(JSON_UNQUOTE(JSON_EXTRACT(data, '$.last_name')), ' ', JSON_UNQUOTE(JSON_EXTRACT(data, '$.first_name')))) LIKE ?", ["%{$searchTerm}%"]);
                    });
                }),
            Column::make('Destinataire', 'data')
                ->format(fn ($value, $row, Column $column) => $value['desired_destination'])
                ->collapseOnTablet(),
            Column::make('Message', 'data')
                ->format(fn ($value, $row, Column $column) => '<div style="max-width: 700px;">' .
                    (new Parsedown())->parse($value['message']) . '</div>')
                ->html()
                ->collapseOnMobile(),
            Column::make('Pièces jointes')
                ->label(fn ($row, Column $column) => $this->formatAttachmentsDownloadLinks($row, 'attachments_general'))
                ->html()
                ->collapseOnTablet(),
            Column::make('Date création', 'created_at')
                ->format(fn ($value, $row, Column $column) => $value->setTimezone('Europe/Paris')->format('d/m/Y H:i'))
                ->sortable()
                ->setSortingPillDirections('Plus ancien en premier', 'Plus récent en premier'),
        ];
    }

    protected function formatContactData(array $data): string
    {
        return 'Nom: <b>' . $data['first_name'] . ' ' . $data['last_name'] . '</b><br>'
            . 'Mail: <b>' . $data['email'] . '</b><br>'
            . ($data['phone_number'] !== 'null' ? 'Téléphone: <b>' . $data['phone_number'] . '</b><br>' : '');
    }

    protected function formatAttachmentsDownloadLinks(LogContactFormMessage $log, string $typeAttachments = 'attachments_general'): ?string
    {
        $attachments = $log->getMedia($typeAttachments);
        $links = '';
        $isFirst = true;
        foreach ($attachments as $attachment) {
            if (! $isFirst) {
                $links .= '<br>';
            }
            $isFirst = false;
            $downloadRoute = route('contact.form.logs.attachment.download', $attachment);
            $fileName = $attachment->file_name;

            $links .= "<a href=\"$downloadRoute\" title=\"Télécharger $fileName\">$fileName</a>";
        }

        return $links;
    }
}
