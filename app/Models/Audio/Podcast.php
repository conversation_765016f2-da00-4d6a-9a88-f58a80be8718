<?php

namespace App\Models\Audio;

use App\Models\Radio\Program;
use App\Models\Radio\RadioStation;
use App\Models\Traits\HasLocation;
use App\Models\Users\User;
use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Facades\Date;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use Okipa\MediaLibraryExt\ExtendsMediaAbilities;
use Spatie\Image\Manipulations;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class Podcast extends Model implements HasMedia
{
    use ExtendsMediaAbilities;
    use HasFactory;
    use HasLocation;
    use InteractsWithMedia;

    public const ANNOUNCEMENT_WIDTH = 170;

    public const TYPE_REPLAY = 'replay';

    public const TYPE_ORIGINAL = 'original';

    public const TYPES = [self::TYPE_REPLAY => 'Replay', self::TYPE_ORIGINAL => 'Original'];

    /** @var string */
    protected $table = 'podcasts';

    /** @var array<int, string> */
    protected $fillable = [
        'program_id',
        'thematic_id',
        'title',
        'tags',
        'description',
        'type',
        'duration', // Seconds
        'published_at',
        'winmedia_audio_source_uploaded',
        'active',
    ];

    /** @var array<string, string> */
    protected $casts = [
        'winmedia_audio_source_uploaded' => 'boolean',
        'published_at' => 'datetime',
    ];

    protected static function boot(): void
    {
        parent::boot();
        static::addGlobalScope('order', static fn (Builder $builder) => $builder->orderByDesc('published_at'));
    }

    protected static function booted(): void
    {
        static::deleting(function (Podcast $podcast) {
            $podcast->contentLocations()->delete();
        });
    }

    /** @SuppressWarnings(PHPMD.UnusedFormalParameter) */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('cover')
            ->singleFile()
            ->acceptsMimeTypes(['image/webp', 'image/jpeg', 'image/png'])
            ->registerMediaConversions(function (?Media $media = null) {
                $this->addMediaConversion('seo')
                    ->fit(Manipulations::FIT_CROP, 600, 600)
                    ->format(Manipulations::FORMAT_WEBP);
                $this->addMediaConversion('player_mobile')
                    ->fit(Manipulations::FIT_CROP, 337, 337)
                    ->format(Manipulations::FORMAT_WEBP);
                $this->addMediaConversion('patchwork')
                    ->fit(Manipulations::FIT_CROP, 228, 228)
                    ->withResponsiveImages()
                    ->format(Manipulations::FORMAT_WEBP);
                $this->addMediaConversion('large')
                    ->fit(Manipulations::FIT_CROP, 172, 172)
                    ->withResponsiveImages()
                    ->format(Manipulations::FORMAT_WEBP);
                $this->addMediaConversion('medium')
                    ->fit(Manipulations::FIT_CROP, 122, 122)
                    ->withResponsiveImages()
                    ->format(Manipulations::FORMAT_WEBP);
                $this->addMediaConversion('small')
                    ->fit(Manipulations::FIT_CROP, 74, 74)
                    ->withResponsiveImages()
                    ->format(Manipulations::FORMAT_WEBP);
                $this->addMediaConversion('background_gradient')
                    ->fit(Manipulations::FIT_CROP, 475, 296)
                    ->withResponsiveImages()
                    ->format(Manipulations::FORMAT_WEBP);
                $this->addMediaConversion('card')
                    ->fit(Manipulations::FIT_CROP, 122, 122)
                    ->format(Manipulations::FORMAT_WEBP);
                $this->addMediaConversion('announcement')
                    ->fit(Manipulations::FIT_CROP, self::ANNOUNCEMENT_WIDTH, 170)
                    ->withResponsiveImages()
                    ->format(Manipulations::FORMAT_WEBP);
            });
        $this->addMediaCollection('audio')->singleFile()->acceptsMimeTypes(['audio/mpeg']);
    }

    /**
     * @throws \Spatie\Image\Exceptions\InvalidManipulation
     *
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function registerMediaConversions(?Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->fit(Manipulations::FIT_CROP, 40, 40)
            ->format(Manipulations::FORMAT_WEBP);
    }

    public function thematic(): BelongsTo
    {
        return $this->belongsTo(Thematic::class, 'thematic_id', 'id');
    }

    public function authors(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'podcasts_authors', 'podcast_id', 'user_id')
            ->withPivot('index')
            ->orderBy('podcasts_authors.index')
            ->withTimestamps();
    }

    public function program(): BelongsTo
    {
        return $this->belongsTo(Program::class, 'program_id', 'id');
    }

    public function radioStations(): BelongsToMany
    {
        return $this->belongsToMany(RadioStation::class, 'podcasts_radio_stations')->withTimestamps();
    }

    public function songs(): BelongsToMany
    {
        return $this->belongsToMany(Song::class, 'podcasts_songs', 'podcast_id', 'song_id')
            ->allTypes()
            ->withPivot('index')
            ->orderBy('podcasts_songs.index')
            ->withTimestamps();
    }

    public function scopeHasAudio(Builder $podcast): void
    {
        $podcast->where(function (Builder $query) {
            $query->whereIn('id', function (\Illuminate\Database\Query\Builder $query) {
                $query->select('media.model_id')
                    ->from('media')
                    ->join('podcasts', 'podcasts.id', '=', 'media.model_id')
                    ->where('media.model_type', self::class)
                    ->where('media.collection_name', 'audio')
                    ->groupBy('media.model_id');
            });
            $query->orWhere('winmedia_audio_source_uploaded', true);
        });
    }

    public function scopeActive(Builder $query): void
    {
        $query->where('active', true);
    }

    public function audioStream(): Attribute
    {
        return new Attribute(
            get: fn () => [
                'hls' => null,
                'dash' => null,
                'mp3' => File::exists(Storage::path('public/winmedia/podcasts/' . $this->id . '.mp3'))
                && $this->winmedia_audio_source_uploaded
                    ? asset('storage/winmedia/podcasts/' . $this->id . '.mp3')
                    : $this->getFirstMediaUrl('audio'),
            ]
        );
    }

    public function radioStationIdsJson(): Attribute
    {
        return new Attribute(
            get: fn () => $this->radioStations->sortBy('name')->pluck('id')->values()->toJson()
        );
    }

    public function authorsIdsJson(): Attribute
    {
        return new Attribute(
            get: fn () => $this->authors->sortBy('index')->pluck('id')->values()->toJson()
        );
    }

    public function humanReadableDuration(): Attribute
    {
        $start = Date::now();

        return new Attribute(
            get: fn () => $start
                ->addSeconds($this->duration)
                ->diffForHumans($start, CarbonInterface::DIFF_ABSOLUTE, true, 2)
        );
    }
}
