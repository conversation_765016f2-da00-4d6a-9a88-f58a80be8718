<?php

namespace Tests\Feature\Livewire\Programs;

use App\Http\Livewire\Programs\Index;
use App\Models\Audio\Podcast;
use App\Models\Audio\Thematic;
use App\Models\Radio\Program;
use App\Models\Radio\RadioStation;
use App\Models\Settings\Settings;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Date;
use Illuminate\Support\Str;
use Livewire\Livewire;
use Parsedown;
use Tests\TestCase;

class IndexTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_cant_display_program_grid_if_not_initialized(): void
    {
        Settings::factory()->create();
        Livewire::test(Index::class)
            ->assertSet('initialized', false)
            ->assertSet('selectedDay', null)
            ->assertSet('allWeeks', [])
            ->assertSet('visibleDays', [])
            ->assertSet('currentWeekOffset', 0)
            ->assertSet('selectedPodcastIds', [])
            ->assertSet('playedAudioSourceClass', null)
            ->assertSet('playedAudioSourceId', null)
            ->assertSet('pauseAllPodcasts', false)
            ->assertSeeInOrder(['Chargement...', 'Chargement en cours...']);
    }

    /** @test */
    public function it_can_set_current_week_days(): void
    {
        Settings::factory()->create();
        RadioStation::factory()->active()->create();
        /** @var \App\Http\Livewire\Programs\Index|\Livewire\Testing\TestableLivewire $component */
        $component = Livewire::test(Index::class)->call('init');
        // Vérifier que la semaine actuelle contient aujourd'hui
        $todayFound = false;
        foreach ($component->allWeeks as $week) {
            if (in_array(Date::today()->toDateString(), $week)) {
                $todayFound = true;
                break;
            }
        }
        $this->assertTrue($todayFound);

        $this->assertContains(
            Date::today()->toDateString(),
            $component->visibleDays,
        );
        $this->assertEquals(Date::now()->toDateString(), $component->selectedDay);
        $this->assertEquals(5, count($component->allWeeks)); // 5 semaines de données
        $this->assertEquals(7, count($component->visibleDays)); // 7 jours visibles (une semaine)
        $this->assertEquals(2, $component->currentWeekOffset); // Semaine actuelle au milieu
    }

    /** @test */
    public function it_can_display_selected_day(): void
    {
        Settings::factory()->create();
        RadioStation::factory()->active()->create();
        $today = Date::today();
        Livewire::test(Index::class)
            ->call('init')
            ->assertSeeInOrder([
                'active',
                rtrim($today->isoFormat('ddd'), '.'),
                $today->format('d'),
            ]);
    }

    /** @test */
    public function it_can_filter_programs_by_day(): void
    {
        Settings::factory()->create();
        Date::setTestNow(Date::now()->startOfWeek());
        Thematic::factory()->create();
        /** @var \App\Models\Radio\RadioStation $radioStation */
        $radioStation = RadioStation::factory()->active()->create();
        Program::factory()->create();
        /** @var \App\Models\Audio\Podcast $podcast */
        $podcast =
            Podcast::factory()->withMedia()->create(['published_at' => Date::now(), 'type' => Podcast::TYPE_REPLAY]);
        $podcast->radioStations()->sync([$radioStation->id]);
        Livewire::test(Index::class)
            ->call('init')
            ->assertSeeHtmlInOrder([
                1,
                $podcast->getFirstMedia('cover')->getFullUrl('thumb'),
                e($podcast->program->title),
                e($podcast->title),
                e(Str::limit(html_entity_decode(strip_tags((new Parsedown())->text($podcast->program->description))))),
                $podcast->published_at->setTimezone('Europe/Paris')->format('G\hi'),
            ])
            ->call('updateSelectedDay', Date::now()->addDay()->toDateString())
            ->assertDontSee([
                $podcast->getFirstMedia('cover')->getFullUrl('thumb'),
                e($podcast->program->title),
                e($podcast->title),
                e(Str::limit(html_entity_decode(strip_tags((new Parsedown())->text($podcast->program->description))))),
                $podcast->published_at->setTimezone('Europe/Paris')->format('G\hi'),
            ], false);
    }

    /** @test */
    public function it_can_navigate_between_weeks(): void
    {
        Settings::factory()->create();
        RadioStation::factory()->active()->create();
        /** @var \App\Http\Livewire\Programs\Index|\Livewire\Testing\TestableLivewire $component */
        $component = Livewire::test(Index::class)->call('init');

        $initialOffset = $component->currentWeekOffset;
        $initialVisibleDays = $component->visibleDays;

        // Test navigation vers la semaine suivante
        $component->call('navigateToNextPeriod');
        $this->assertEquals($initialOffset + 1, $component->currentWeekOffset);
        $this->assertNotEquals($initialVisibleDays, $component->visibleDays);

        // Test navigation vers la semaine précédente
        $component->call('navigateToPreviousPeriod');
        $this->assertEquals($initialOffset, $component->currentWeekOffset);
        $this->assertEquals($initialVisibleDays, $component->visibleDays);
    }

    /** @test */
    public function it_can_check_navigation_boundaries(): void
    {
        Settings::factory()->create();
        RadioStation::factory()->active()->create();
        /** @var \App\Http\Livewire\Programs\Index|\Livewire\Testing\TestableLivewire $component */
        $component = Livewire::test(Index::class)->call('init');

        // Au début (semaine actuelle), on peut naviguer dans les deux sens
        $this->assertTrue($component->canNavigateToNext());
        $this->assertTrue($component->canNavigateToPrevious());

        // Navigation vers la première semaine
        $component->call('navigateToPreviousPeriod');
        $component->call('navigateToPreviousPeriod');

        // On ne peut plus naviguer vers le précédent
        $this->assertFalse($component->canNavigateToPrevious());
        $this->assertTrue($component->canNavigateToNext());

        // Navigation vers la dernière semaine
        $component->call('navigateToNextPeriod');
        $component->call('navigateToNextPeriod');
        $component->call('navigateToNextPeriod');
        $component->call('navigateToNextPeriod');

        // On ne peut plus naviguer vers le suivant
        $this->assertFalse($component->canNavigateToNext());
        $this->assertTrue($component->canNavigateToPrevious());
    }

    /** @test */
    public function it_keeps_selected_day_when_navigating_between_weeks(): void
    {
        Settings::factory()->create();
        RadioStation::factory()->active()->create();
        /** @var \App\Http\Livewire\Programs\Index|\Livewire\Testing\TestableLivewire $component */
        $component = Livewire::test(Index::class)->call('init');

        $initialSelectedDay = $component->selectedDay;

        // Navigation vers la semaine suivante
        $component->call('navigateToNextPeriod');

        // Le jour sélectionné ne devrait pas changer
        $this->assertEquals($initialSelectedDay, $component->selectedDay);

        // Navigation vers la semaine précédente (retour)
        $component->call('navigateToPreviousPeriod');

        // Le jour sélectionné devrait toujours être le même
        $this->assertEquals($initialSelectedDay, $component->selectedDay);
        $this->assertContains($component->selectedDay, $component->visibleDays);
    }

    /** @test */
    public function it_handles_selected_day_not_in_visible_week(): void
    {
        Settings::factory()->create();
        RadioStation::factory()->active()->create();
        /** @var \App\Http\Livewire\Programs\Index|\Livewire\Testing\TestableLivewire $component */
        $component = Livewire::test(Index::class)->call('init');

        $initialSelectedDay = $component->selectedDay;

        // Navigation vers une semaine où le jour sélectionné n'est pas visible
        // (par exemple, aller 2 semaines en arrière)
        $component->call('navigateToPreviousPeriod');
        $component->call('navigateToPreviousPeriod');

        // Le jour sélectionné ne devrait pas changer même s'il n'est pas visible
        $this->assertEquals($initialSelectedDay, $component->selectedDay);
        $this->assertNotContains($component->selectedDay, $component->visibleDays);

        // Retour à la semaine actuelle
        $component->call('navigateToNextPeriod');
        $component->call('navigateToNextPeriod');

        // Le jour sélectionné devrait toujours être le même et maintenant visible
        $this->assertEquals($initialSelectedDay, $component->selectedDay);
        $this->assertContains($component->selectedDay, $component->visibleDays);
    }
}
