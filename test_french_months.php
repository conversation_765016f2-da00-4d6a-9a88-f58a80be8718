<?php

require_once 'vendor/autoload.php';

use Illuminate\Support\Facades\Date;

// Configuration de la locale française
setlocale(LC_TIME, 'fr_FR.UTF-8');
Date::setLocale('fr');

// Test de l'affichage des mois en français
echo "Test de l'affichage des mois en français:\n\n";

// Simuler quelques dates
$dates = [
    '2025-06-01', // 1er juin
    '2025-06-15', // 15 juin
    '2025-07-01', // 1er juillet
    '2025-07-15', // 15 juillet
    '2025-08-01', // 1er août
];

foreach ($dates as $date) {
    $parsedDate = Date::parse($date);
    
    echo "Date: $date\n";
    echo "  format('M'): " . $parsedDate->format('M') . "\n";
    echo "  isoFormat('MMM'): " . $parsedDate->isoFormat('MMM') . "\n";
    echo "  isoFormat('MMMM'): " . $parsedDate->isoFormat('MMMM') . "\n";
    echo "  isoFormat('ddd'): " . rtrim($parsedDate->isoFormat('ddd'), '.') . "\n";
    echo "\n";
}

echo "Test de la condition d'affichage du mois:\n";
foreach ($dates as $index => $date) {
    $parsedDate = Date::parse($date);
    $day = $parsedDate->format('d');
    $isFirst = $index === 0;
    
    $shouldShowMonth = ($day == '1' || $isFirst);
    
    echo "Date: $date (jour $day, premier: " . ($isFirst ? 'oui' : 'non') . ")\n";
    echo "  Afficher le mois ? " . ($shouldShowMonth ? 'OUI' : 'NON') . "\n";
    if ($shouldShowMonth) {
        echo "  Mois affiché: " . $parsedDate->isoFormat('MMM') . "\n";
    }
    echo "\n";
}
