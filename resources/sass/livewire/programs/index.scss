// Components
@import '../../components/front/audio/program-line';

.programs-navigation-container {
    .navigation-btn {
        min-width: 40px;
        height: 40px;
        border-radius: 50%;
        transition: all 0.3s ease;

        &:not(:disabled):hover {
            background-color: $secondary;
            border-color: $secondary;
            color: white;
        }

        &:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
    }
}

#programs-day-selector {
    overflow-x: auto;
    scrollbar-width: none; // Firefox
    -ms-overflow-style: none; // IE/Edge

    &::-webkit-scrollbar {
        display: none; // Chrome/Safari
    }

    .day {
        width: pxToRem(65);
        height: pxToRem(81);
        flex-shrink: 0; // Empêche la compression des jours
        background: none;

        &:not(.active) {
            .content {
                background-color: $gray-300;
            }
        }

        &.active, &:hover {
            .content {
                background-color: $secondary !important;
                color: white;
                height: pxToRem(58) !important;
                transition-duration: 0.5s;
            }

            &:after {
                display: inline-block;
                content: '';
                border-top: pxToRem(5) solid $primary;
                position: absolute;
                width: 50%;
                left: 50%;
                transform: translateX(-50%);
                bottom: 0;
            }
        }
    }
}



// Responsive adjustments
@media (max-width: 768px) {
    .programs-navigation-container {
        .navigation-btn {
            min-width: 35px !important;
            height: 35px !important;
        }
    }

    #programs-day-selector {
        .day {
            width: pxToRem(55);
            height: pxToRem(71);
            margin: 0 pxToRem(4) !important;

            .content {
                font-size: 0.9rem;

                p {
                    font-size: 0.9rem !important;

                    &.small {
                        font-size: 0.75rem !important;
                    }
                }
            }
        }
    }
}

