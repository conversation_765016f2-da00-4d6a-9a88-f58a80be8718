<div wire:init="init" class="d-flex flex-column py-4">
    {{-- Search Form --}}
    <div class="pb-3 mb-3">
        <livewire:search.form :wire:key="uniqid('podcasts-list-search-form', true)"/>
    </div>
    <div wire:ignore class="pb-3 mb-3 radio-station-card-programs">
        <livewire:radio-stations.rail :wire:key="uniqid('programs-radio-stations', true)" :isMain="false"/>
    </div>
    <div class="pb-3 mb-3">
        <h2 class="h2 fw-bold mb-3">Cette semaine</h2>
        @if($initialized)
            <div class="row flex-column-reverse flex-lg-row align-items-center">
                <div class="col-lg-9">
                    {{-- Navigation avec boutons (Desktop et Mobile) --}}
                    <div class="programs-navigation-container d-flex align-items-center">
                        {{-- Bouton précédent --}}
                        <button
                            wire:click="navigateToPreviousPeriod"
                            class="btn btn-outline-secondary navigation-btn me-2 d-flex align-items-center justify-content-center"
                            @if(!$this->canNavigateToPrevious()) disabled @endif
                            title="Semaine précédente">
                            <x:front.icon name="chevron-left" />
                        </button>

                        {{-- Sélecteur de jours --}}
                        <div id="programs-day-selector" class="d-flex justify-content-start align-items-start flex-grow-1">
                            @foreach($visibleDays as $day)
                                <div class="@if($selectedDay === $day) active @endif position-relative day cursor-pointer mx-1 mx-lg-2 {{ $loop->first ? 'ms-lg-0' : null }}"
                                     wire:click="updateSelectedDay('{{ $day }}')">
                                    <div
                                        class="h-100 rounded-3 content d-flex flex-column justify-content-center align-items-center w-100">
                                        <p class="m-0 h4 text-uppercase">{{ rtrim(Date::parse($day)->isoFormat('ddd'), '.')}}</p>
                                        <p class="m-0 h4 fw-bold">{{ Date::parse($day)->format('d') }}</p>
                                        @if(Date::parse($day)->format('d') == '1' || $loop->first)
                                            <p class="m-0 small text-muted">{{ Date::parse($day)->isoFormat('MMM') }}</p>
                                        @endif
                                    </div>
                                </div>
                            @endforeach
                        </div>

                        {{-- Bouton suivant --}}
                        <button
                            wire:click="navigateToNextPeriod"
                            class="btn btn-outline-secondary navigation-btn ms-2 d-flex align-items-center justify-content-center"
                            @if(!$this->canNavigateToNext()) disabled @endif
                            title="Semaine suivante">
                            <x:front.icon name="chevron-right" />
                        </button>
                    </div>
                </div>
                @if($programSchedule = settings()->getFirstMedia('program_schedule'))
                    <div class="col-lg-3 mb-4 mb-lg-0">
                        <a href="{{ $programSchedule->getFullUrl() }}"
                           download="programme-hebdomadaire.pdf"
                           class="btn btn-primary">
                            <div class="d-flex">
                                <span class="me-3">Télécharger le programme</span>
                                <x:front.icon name="download"/>
                            </div>
                        </a>
                    </div>
                @endif
            </div>
            <div class="row mt-4">
                <div class="col-12">
                    <div>
                        @foreach($podcasts->reject(fn(\App\Models\Audio\Podcast $podcast) => $podcast->program->main_program_id) as $podcast)
                            <x:front.audio.program-line :wire:key="uniqid('program-line-'.$podcast->program->id, true)"
                                                        :lineNumber="$loop->index + 1"
                                                        :selected="$podcast->programSelected ?? false"
                                                        :playing="$podcast->playing ?? false"
                                                        :podcast="$podcast"
                                                        :subPodcasts="$podcast->subPodcasts"
                                                        :isDark="$loop->index % 2 !== 0"/>
                        @endforeach
                    </div>
                </div>
            </div>
        @else
            <div class="d-flex align-items-center">
                <div class="spinner-border me-3" role="status">
                    <span class="visually-hidden">Chargement...</span>
                </div>
                Chargement en cours...
            </div>
        @endif
    </div>
</div>
